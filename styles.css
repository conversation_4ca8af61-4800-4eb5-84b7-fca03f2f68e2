/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff6b35;
    --secondary-color: #ffd700;
    --accent-color: #ff4757;
    --dark-color: #1a1a1a;
    --light-color: #ffffff;
    --gray-color: #6c757d;
    --success-color: #28a745;
    --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #ff4757 100%);
    --gradient-secondary: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--dark-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.header .logo img {
    width: 120px;
    height: auto;
    transition: var(--transition);
}

.header .logo img:hover {
    transform: scale(1.05);
}

.menu {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--light-color);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--light-color);
    transform: translateY(-2px);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-xl {
    padding: 20px 40px;
    font-size: 18px;
    font-weight: 700;
}

.primary-cta {
    background: var(--gradient-secondary);
    color: var(--dark-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 215, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Hero Section */
.hero {
    background: var(--light-color);
    padding: 80px 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ff6b35" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    text-align: left;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--dark-color);
    line-height: 1.1;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-description {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 30px;
    line-height: 1.7;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 40px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.feature-icon {
    font-size: 1.5rem;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.trust-indicators {
    padding: 20px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--success-color);
}

.trust-text {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--success-color);
    font-weight: 600;
    margin: 0;
}

.trust-icon {
    font-size: 1.2rem;
}

.hero-image {
    position: relative;
    text-align: center;
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transition: var(--transition);
}

.hero-image img:hover {
    transform: scale(1.05);
}

.floating-stats {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat {
    background: var(--light-color);
    padding: 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    text-align: center;
    min-width: 80px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--gray-color);
    font-weight: 600;
}

/* Section Styles */
.benefits, .testimonials, .cta-section {
    padding: 80px 0;
}

.benefits {
    background: #f8f9fa;
}

.testimonials {
    background: var(--light-color);
}

.cta-section {
    background: var(--gradient-primary);
    color: var(--light-color);
    text-align: center;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.cta-section .section-header h2 {
    color: var(--light-color);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-color);
    max-width: 600px;
    margin: 0 auto;
}

/* Benefits Grid */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.benefit-card {
    background: var(--light-color);
    padding: 40px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.benefit-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.benefit-card p {
    color: var(--gray-color);
    line-height: 1.6;
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: var(--light-color);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--secondary-color);
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.testimonial-rating {
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.testimonial-card p {
    font-style: italic;
    color: var(--gray-color);
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-author {
    font-weight: 600;
    color: var(--dark-color);
}

/* CTA Section */
.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    line-height: 1.6;
}

.urgency-timer {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.timer-label {
    display: block;
    font-size: 1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.timer {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.cta-guarantee {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0.9;
}

.guarantee-icon {
    font-size: 1.2rem;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--light-color);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
    color: var(--secondary-color);
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: inline-block;
    font-size: 1.5rem;
    transition: var(--transition);
}

.social-links a:hover {
    transform: scale(1.2);
}

.footer-nav {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-nav a {
    color: #ccc;
    text-decoration: none;
    transition: var(--transition);
}

.footer-nav a:hover {
    color: var(--secondary-color);
    padding-left: 5px;
}

.payment-methods {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.payment-icon {
    font-size: 2rem;
}

.payment-text {
    font-size: 0.9rem;
    color: #ccc;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    text-align: center;
    color: #ccc;
}

.footer-bottom a {
    color: var(--secondary-color);
    text-decoration: none;
}

.disclaimer {
    font-size: 0.8rem;
    margin-top: 10px;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text {
        text-align: center;
    }

    .hero-text h1 {
        font-size: 3rem;
    }

    .floating-stats {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .header {
        padding: 15px 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .menu {
        width: 100%;
        justify-content: center;
    }

    .hero {
        padding: 60px 0;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-large, .btn-xl {
        width: 100%;
        max-width: 300px;
    }

    .benefits, .testimonials, .cta-section {
        padding: 60px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-nav {
        align-items: center;
    }

    .social-links {
        justify-content: center;
    }

    .payment-methods {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .benefit-card, .testimonial-card {
        padding: 25px 20px;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .timer {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .btn-large {
        padding: 14px 28px;
        font-size: 14px;
    }

    .btn-xl {
        padding: 16px 32px;
        font-size: 16px;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Accessibility */
.btn:focus,
.footer-nav a:focus,
.social-links a:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000;
        --secondary-color: #000;
        --gray-color: #000;
    }

    .hero-text h1 {
        -webkit-text-fill-color: initial;
        color: var(--dark-color);
    }
}

/* Print Styles */
@media print {
    .header, .footer, .cta-section {
        display: none;
    }

    .hero, .benefits, .testimonials {
        page-break-inside: avoid;
    }

    body {
        background: white;
        color: black;
    }
}
