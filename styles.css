* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 10px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #000;
    padding: 20px;
    flex-wrap: wrap;
}

.header .logo img {
    width: 80px;
}

.menu {
    display: flex;
    margin-top: 10px;
}

.btn {
    padding: 10px 20px;
    margin-left: 10px;
    background-color: #e60000;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    flex-wrap: wrap;
}

.hero-text {
    width: 100%;
    text-align: center;
}

.hero-text h1 {
    font-size: 30px;
    font-weight: bold;
    color: #e60000;
}

.hero-text p {
    font-size: 18px;
    font-weight: bold;
    color: #f8d500;
}

.hero-image {
    width: 100%;
    text-align: center;
    margin-top: 20px;
}

.hero-image img {
    width: 90%;
    height: auto;
}

.footer {
    margin-top: 30px;
    background-color: #000;
    padding: 20px;
    text-align: center;
}

.footer-menu {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-menu a {
    color: #fff;
    margin: 10px;
    text-decoration: none;
    font-size: 16px;
}

.footer-menu a:hover {
    color: #f8d500;
}

/* Media Queries for Mobile */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        align-items: center;
    }

    .hero {
        flex-direction: column;
    }

    .footer-menu a {
        font-size: 14px;
        margin: 5px;
    }
}
